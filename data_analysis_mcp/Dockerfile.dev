# 开发环境Dockerfile
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 安装调试工具
RUN pip install debugpy ipdb

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY pyproject.toml uv.lock ./

# 安装所有依赖（包括开发依赖）
RUN uv sync --frozen

# 设置环境变量
ENV PYTHONPATH=/app/src \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    LOG_LEVEL=DEBUG

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/config

# 暴露端口
EXPOSE 8000 5678

# 默认命令（可以被docker-compose覆盖）
CMD ["python", "-m", "data_analysis_mcp.server"]
