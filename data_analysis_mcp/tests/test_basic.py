from __future__ import annotations

import json
from pathlib import Path

from data_analysis_mcp.core.engine import DataAnalysisEngine


def test_structure_on_small_csv(tmp_path: Path):
    csv = tmp_path / "a.csv"
    csv.write_text("x,y\n1,2\n3,4\n", encoding="utf-8")
    eng = DataAnalysisEngine(max_rows=100)
    rr = eng.read(str(csv))
    s = eng.structure(rr)
    assert s["columns"] == 2
    assert s["rows"] == 2


def test_statistics_basic(tmp_path: Path):
    csv = tmp_path / "b.csv"
    csv.write_text("x,y\n1,2\n3,5\n", encoding="utf-8")
    eng = DataAnalysisEngine(max_rows=100)
    rr = eng.read(str(csv))
    stats = eng.statistics(rr)
    assert "numeric_describe" in stats

