# 数据分析MCP服务 - 调试和部署指南

## 📋 目录

1. [快速开始](#快速开始)
2. [配置文件说明](#配置文件说明)
3. [调试方法](#调试方法)
4. [部署选项](#部署选项)
5. [故障排除](#故障排除)
6. [性能优化](#性能优化)

## 🚀 快速开始

### 本地开发环境设置

```bash
# 1. 克隆项目
git clone <repository-url>
cd data_analysis_mcp

# 2. 安装依赖
uv sync

# 3. 运行测试
uv run pytest -v

# 4. 启动开发服务器
uv run python -m data_analysis_mcp.server
```

### 使用调试脚本

```bash
# 运行引擎调试脚本
uv run python debug_engine.py

# 查看调试日志
tail -f debug_engine.log
```

## 📁 配置文件说明

### 主配置文件 (`config/project_config.json`)

包含完整的项目配置，包括：
- 项目元信息
- 开发环境设置
- MCP服务器配置
- 工具定义
- 客户端配置
- 测试配置
- 部署配置
- 监控配置

### 调试配置 (`config/debug_config.json`)

专门用于调试的配置：
- 详细的日志设置
- 组件级别的调试开关
- 性能分析配置
- 测试数据配置
- 错误处理配置
- 安全设置

### VS Code调试配置 (`.vscode/launch.json`)

包含多种调试场景：
- 调试MCP服务器
- 调试测试
- 调试特定组件
- 性能分析

## 🔧 调试方法

### 1. 本地调试

#### 使用VS Code调试
1. 打开VS Code
2. 选择调试配置："Debug MCP Server"
3. 设置断点
4. 按F5开始调试

#### 使用命令行调试
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
uv run python -m data_analysis_mcp.server

# 使用pdb调试
uv run python -m pdb -m data_analysis_mcp.server
```

### 2. 组件级调试

#### 调试数据引擎
```bash
# 运行引擎调试脚本
uv run python debug_engine.py

# 或者在Python中
python -c "
from data_analysis_mcp.core.engine import DataAnalysisEngine
engine = DataAnalysisEngine()
# 进行调试操作
"
```

#### 调试特定解析器
```bash
# 测试CSV解析器
python -c "
from data_analysis_mcp.parsers.csv import CSVParser
parser = CSVParser()
# 测试解析功能
"
```

### 3. MCP协议调试

#### 启用协议日志
在配置中设置：
```json
{
  "mcp_debug": {
    "protocol_logging": true,
    "message_validation": true
  }
}
```

#### 模拟客户端请求
```bash
# 使用curl测试（如果有HTTP接口）
curl -X POST http://localhost:8000/tools/analyze_file \
  -H "Content-Type: application/json" \
  -d '{"file_path": "test.csv", "analysis_type": "basic"}'
```

## 🐳 部署选项

### 1. Docker部署

#### 开发环境
```bash
# 构建并启动开发环境
docker-compose up data-analysis-mcp-dev

# 连接调试器（VS Code）
# 在VS Code中连接到localhost:5678
```

#### 生产环境
```bash
# 构建并启动生产环境
docker-compose up -d data-analysis-mcp

# 查看日志
docker-compose logs -f data-analysis-mcp
```

### 2. 直接部署

#### 使用uv
```bash
# 安装到系统
uv install data-analysis-mcp

# 启动服务
data-analysis-mcp
```

#### 使用systemd（Linux）
创建服务文件 `/etc/systemd/system/data-analysis-mcp.service`：
```ini
[Unit]
Description=Data Analysis MCP Server
After=network.target

[Service]
Type=simple
User=mcp
WorkingDirectory=/opt/data-analysis-mcp
Environment=PYTHONPATH=/opt/data-analysis-mcp/src
ExecStart=/usr/local/bin/uv run python -m data_analysis_mcp.server
Restart=always

[Install]
WantedBy=multi-user.target
```

### 3. Claude Desktop集成

#### 基本配置
```json
{
  "mcpServers": {
    "data-analysis": {
      "command": "uv",
      "args": ["run", "python", "-m", "data_analysis_mcp.server"],
      "env": {
        "PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src"
      }
    }
  }
}
```

#### 开发配置
```json
{
  "mcpServers": {
    "data-analysis-dev": {
      "command": "uv",
      "args": ["run", "python", "-m", "data_analysis_mcp.server"],
      "env": {
        "PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src",
        "LOG_LEVEL": "DEBUG",
        "MAX_ROWS": "1000"
      }
    }
  }
}
```

## 🔍 故障排除

### 常见问题

#### 1. 导入错误
```bash
# 检查PYTHONPATH
echo $PYTHONPATH

# 确保包含src目录
export PYTHONPATH="${PWD}/src:$PYTHONPATH"
```

#### 2. 依赖问题
```bash
# 重新同步依赖
uv sync --reinstall

# 检查依赖冲突
uv tree
```

#### 3. 文件权限问题
```bash
# 检查文件权限
ls -la data/

# 修复权限
chmod 644 data/*.csv
```

#### 4. 内存问题
```bash
# 限制处理行数
export MAX_ROWS=1000

# 监控内存使用
htop
```

### 日志分析

#### 查看应用日志
```bash
# 实时查看日志
tail -f logs/app.log

# 搜索错误
grep ERROR logs/app.log

# 分析性能
grep "processing_time" logs/app.log
```

#### 调试特定组件
```bash
# 只看引擎日志
grep "DataAnalysisEngine" logs/debug.log

# 只看解析器日志
grep "Parser" logs/debug.log
```

## ⚡ 性能优化

### 1. 配置优化

#### 调整处理限制
```json
{
  "max_rows": 50000,
  "timeout": 300,
  "memory_limit": "2GB"
}
```

#### 启用缓存
```json
{
  "cache_enabled": true,
  "cache_ttl": 3600
}
```

### 2. 代码优化

#### 使用性能分析
```bash
# 运行性能分析
uv run python -m cProfile -o profile.stats -m data_analysis_mcp.server

# 分析结果
uv run python -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(20)
"
```

#### 内存分析
```bash
# 安装内存分析工具
uv add memory-profiler

# 运行内存分析
uv run mprof run python -m data_analysis_mcp.server
uv run mprof plot
```

### 3. 监控设置

#### 使用Prometheus和Grafana
```bash
# 启动监控栈
docker-compose up -d prometheus grafana

# 访问Grafana
open http://localhost:3000
```

#### 自定义指标
在代码中添加指标收集：
```python
from prometheus_client import Counter, Histogram

REQUEST_COUNT = Counter('requests_total', 'Total requests')
REQUEST_DURATION = Histogram('request_duration_seconds', 'Request duration')
```

## 📞 获取帮助

- 查看项目文档：`docs/`
- 运行测试：`uv run pytest -v`
- 检查代码质量：`uv run ruff check`
- 提交Issue：GitHub Issues
- 社区讨论：GitHub Discussions
