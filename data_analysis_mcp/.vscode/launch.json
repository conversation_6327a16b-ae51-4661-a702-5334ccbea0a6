{"version": "0.2.0", "configurations": [{"name": "Debug MCP Server", "type": "python", "request": "launch", "module": "data_analysis_mcp.server", "console": "integratedTerminal", "cwd": "${workspaceFolder}/data_analysis_mcp", "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "LOG_LEVEL": "DEBUG", "PYTHONDONTWRITEBYTECODE": "1"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}, {"name": "Debug MCP Server (Limited Rows)", "type": "python", "request": "launch", "module": "data_analysis_mcp.server", "console": "integratedTerminal", "cwd": "${workspaceFolder}/data_analysis_mcp", "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "LOG_LEVEL": "DEBUG", "MAX_ROWS": "1000", "PYTHONDONTWRITEBYTECODE": "1"}, "args": [], "justMyCode": false}, {"name": "Debug Tests", "type": "python", "request": "launch", "module": "pytest", "console": "integratedTerminal", "cwd": "${workspaceFolder}/data_analysis_mcp", "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "LOG_LEVEL": "DEBUG"}, "args": ["-v", "--tb=short", "tests/"], "justMyCode": false}, {"name": "Debug Specific Test", "type": "python", "request": "launch", "module": "pytest", "console": "integratedTerminal", "cwd": "${workspaceFolder}/data_analysis_mcp", "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "LOG_LEVEL": "DEBUG"}, "args": ["-v", "--tb=long", "${file}"], "justMyCode": false}, {"name": "Debug Engine Only", "type": "python", "request": "launch", "program": "${workspaceFolder}/data_analysis_mcp/debug_engine.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/data_analysis_mcp", "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "LOG_LEVEL": "DEBUG"}, "args": [], "justMyCode": true}, {"name": "Profile MCP Server", "type": "python", "request": "launch", "module": "cProfile", "console": "integratedTerminal", "cwd": "${workspaceFolder}/data_analysis_mcp", "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "LOG_LEVEL": "INFO"}, "args": ["-o", "profile_output.prof", "-m", "data_analysis_mcp.server"], "justMyCode": false}]}