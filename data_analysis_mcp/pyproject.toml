[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "data-analysis-mcp"
version = "0.1.0"
description = "高性能数据分析MCP服务，支持多种数据格式和本地分析能力"
readme = "README.md"
license = "MIT"
authors = [
    { name = "Data Analysis MCP Team", email = "<EMAIL>" }
]
maintainers = [
    { name = "Data Analysis MCP Team", email = "<EMAIL>" }
]
keywords = [
    "mcp",
    "data-analysis", 
    "excel",
    "csv",
    "statistics",
    "visualization"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Information Analysis",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.10"
dependencies = [
    # MCP框架
    "mcp>=1.0.0",
    
    # 数据处理核心库
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "polars>=0.20.0",  # 高性能数据处理
    
    # 文件格式支持
    "openpyxl>=3.1.0",  # Excel支持
    "xlrd>=2.0.0",      # 旧版Excel支持
    "pyarrow>=14.0.0",  # Parquet支持
    "fastparquet>=2023.10.0",  # Parquet备选
    
    # 数据库支持
    "sqlalchemy>=2.0.0",

    # 统计分析
    "scipy>=1.11.0",
    "scikit-learn>=1.3.0",
    "statsmodels>=0.14.0",
    
    # 可视化
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.17.0",
    
    # 报告生成
    "jinja2>=3.1.0",
    "weasyprint>=60.0",  # PDF生成
    
    # 工具库
    "pydantic>=2.0.0",
    "typer>=0.9.0",
    "rich>=13.0.0",
    "loguru>=0.7.0",
    
    # 性能优化
    "numba>=0.58.0",
    "cython>=3.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
    "mkdocstrings[python]>=0.23.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "hypothesis>=6.82.0",
]
performance = [
    "dask[complete]>=2023.9.0",
    "ray[data]>=2.7.0",
    "modin[all]>=0.22.0",
]

[project.urls]
Homepage = "https://github.com/example/data-analysis-mcp"
Documentation = "https://data-analysis-mcp.readthedocs.io"
Repository = "https://github.com/example/data-analysis-mcp"
Issues = "https://github.com/example/data-analysis-mcp/issues"
Changelog = "https://github.com/example/data-analysis-mcp/blob/main/CHANGELOG.md"

[project.scripts]
data-analysis-mcp = "data_analysis_mcp.cli:main"

[project.entry-points."data_analysis_mcp.parsers"]
excel = "data_analysis_mcp.parsers.excel:ExcelParser"
csv = "data_analysis_mcp.parsers.csv:CSVParser"
json = "data_analysis_mcp.parsers.json:JSONParser"
parquet = "data_analysis_mcp.parsers.parquet:ParquetParser"
sqlite = "data_analysis_mcp.parsers.sqlite:SQLiteParser"

[project.entry-points."data_analysis_mcp.analyzers"]
statistics = "data_analysis_mcp.analyzers.statistics:StatisticsAnalyzer"
quality = "data_analysis_mcp.analyzers.quality:QualityAnalyzer"
correlation = "data_analysis_mcp.analyzers.correlation:CorrelationAnalyzer"
distribution = "data_analysis_mcp.analyzers.distribution:DistributionAnalyzer"

[project.entry-points."data_analysis_mcp.generators"]
html = "data_analysis_mcp.generators.html:HTMLGenerator"
pdf = "data_analysis_mcp.generators.pdf:PDFGenerator"
json = "data_analysis_mcp.generators.json:JSONGenerator"
sql = "data_analysis_mcp.generators.sql:SQLGenerator"

[tool.hatch.build.targets.wheel]
packages = ["src/data_analysis_mcp"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/docs",
    "/examples",
    "README.md",
    "LICENSE",
    "CHANGELOG.md",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=data_analysis_mcp",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--strict-markers",
    "--strict-config",
    "-ra",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src/data_analysis_mcp"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.black]
line-length = 88
target-version = ["py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["data_analysis_mcp"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pandas.*",
    "numpy.*",
    "matplotlib.*",
    "seaborn.*",
    "plotly.*",
    "openpyxl.*",
    "xlrd.*",
    "pyarrow.*",
    "fastparquet.*",
    "scipy.*",
    "sklearn.*",
    "statsmodels.*",
    "weasyprint.*",
    "numba.*",
]
ignore_missing_imports = true

[tool.ruff]
line-length = 88
target-version = "py310"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011"]
