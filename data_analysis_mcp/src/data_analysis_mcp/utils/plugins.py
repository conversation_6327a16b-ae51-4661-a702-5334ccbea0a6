from __future__ import annotations
from importlib.metadata import entry_points
from typing import Dict, Any


def load_plugins(group: str) -> Dict[str, Any]:
    """Load plugin classes/functions registered under an entry points group.

    Returns a mapping {name: object}.
    """
    eps = entry_points()
    # New importlib.metadata API uses select
    matches = eps.select(group=group) if hasattr(eps, "select") else eps.get(group, [])
    loaded = {}
    for ep in matches:
        try:
            loaded[ep.name] = ep.load()
        except Exception as e:
            # Best-effort loading; skip broken plugins
            loaded[ep.name] = {"error": f"Failed to load plugin: {e}"}
    return loaded

