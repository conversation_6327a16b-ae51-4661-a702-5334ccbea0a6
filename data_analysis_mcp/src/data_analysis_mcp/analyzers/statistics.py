from __future__ import annotations
from typing import Any, Dict, Optional, List
import numpy as np
import pandas as pd


def compute_statistics(df: pd.DataFrame, columns: Optional[List[str]] = None) -> Dict[str, Any]:
    if columns:
        df = df[columns]
    stats: Dict[str, Any] = {
        "shape": df.shape,
        "columns": list(df.columns),
        "dtypes": {c: str(t) for c, t in df.dtypes.items()},
        "missing": df.isna().sum().to_dict(),
    }
    num_cols = df.select_dtypes(include=[np.number]).columns
    if len(num_cols) > 0:
        stats["numeric_describe"] = df[num_cols].describe().to_dict()
    obj_cols = df.select_dtypes(include=["object"]).columns
    if len(obj_cols) > 0:
        topk = {}
        for c in obj_cols[:10]:
            vc = df[c].value_counts(dropna=True).head(3)
            topk[c] = {str(k): int(v) for k, v in vc.to_dict().items()}
        stats["top_values"] = topk
    return stats

