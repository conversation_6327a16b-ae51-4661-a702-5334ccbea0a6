from __future__ import annotations
from typing import Any, Dict
import numpy as np
import pandas as pd


def check_quality(df: pd.DataFrame) -> Dict[str, Any]:
    res: Dict[str, Any] = {
        "rows": int(df.shape[0]),
        "columns": int(df.shape[1]),
        "missing": df.isna().sum().to_dict(),
        "duplicate_rows": int(df.duplicated().sum()),
    }
    outliers: Dict[str, int] = {}
    for c in df.select_dtypes(include=[np.number]).columns:
        s = df[c].dropna()
        if s.empty:
            outliers[c] = 0
            continue
        q1, q3 = s.quantile(0.25), s.quantile(0.75)
        iqr = q3 - q1
        lower, upper = q1 - 1.5 * iqr, q3 + 1.5 * iqr
        outliers[c] = int(((s < lower) | (s > upper)).sum())
    res["outliers_iqr"] = outliers
    return res

