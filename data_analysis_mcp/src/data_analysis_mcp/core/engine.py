from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import numpy as np


DataObject = Union[pd.DataFrame, Dict[str, pd.DataFrame]]


@dataclass
class ReadResult:
    file_path: str
    file_type: str
    data: DataObject


class DataAnalysisEngine:
    """
    轻量高可用的数据分析引擎：
    - 统一文件读取入口
    - 统计分析与数据质量检查
    - SQL表结构建议生成
    """

    SUPPORTED_TYPES = {".csv": "csv", ".xlsx": "excel", ".xls": "excel", ".json": "json", ".parquet": "parquet"}

    def __init__(self, max_rows: int = 10000) -> None:
        self.max_rows = max_rows

    # -------- File IO --------
    def read(self, file_path: str) -> ReadResult:
        path = Path(file_path)
        if not path.exists() or not path.is_file():
            raise FileNotFoundError(f"File not found: {file_path}")
        if ".." in str(path):
            raise ValueError("Invalid path: contains '..'")

        ext = path.suffix.lower()
        ftype = self.SUPPORTED_TYPES.get(ext, "unknown")
        if ftype == "unknown":
            raise ValueError(f"Unsupported file type: {ext}")

        if ftype == "csv":
            df = pd.read_csv(path, nrows=self.max_rows)
            data: DataObject = df
        elif ftype == "excel":
            # read all sheets (limited rows per sheet)
            data = {}
            x = pd.ExcelFile(path)
            for sheet in x.sheet_names:
                data[sheet] = pd.read_excel(path, sheet_name=sheet, nrows=self.max_rows)
        elif ftype == "json":
            # try to load as records array or single object
            obj = pd.read_json(path, lines=False, typ="frame")
            data = obj
        elif ftype == "parquet":
            try:
                df = pd.read_parquet(path)
            except Exception as e:
                raise RuntimeError(f"Failed to read parquet: {e}")
            data = df.head(self.max_rows)
        else:
            raise ValueError(f"Unhandled file type: {ftype}")

        return ReadResult(str(path), ftype, data)

    # -------- Structure --------
    def structure(self, rr: ReadResult) -> Dict[str, Any]:
        if isinstance(rr.data, dict):
            return {
                "file_path": rr.file_path,
                "file_type": rr.file_type,
                "sheets": {
                    name: {
                        "rows": int(df.shape[0]),
                        "columns": int(df.shape[1]),
                        "column_names": list(map(str, df.columns)),
                    }
                    for name, df in rr.data.items()
                },
            }
        else:
            df = rr.data
            return {
                "file_path": rr.file_path,
                "file_type": rr.file_type,
                "rows": int(df.shape[0]),
                "columns": int(df.shape[1]),
                "column_names": list(map(str, df.columns)),
            }

    # -------- Statistics --------
    def statistics(self, rr: ReadResult, sheet: Optional[str] = None, columns: Optional[List[str]] = None) -> Dict[str, Any]:
        df = self._select_df(rr, sheet)
        if columns:
            df = df[columns]
        stats: Dict[str, Any] = {
            "shape": df.shape,
            "columns": list(df.columns),
            "dtypes": {c: str(t) for c, t in df.dtypes.items()},
            "missing": df.isna().sum().to_dict(),
        }
        num_cols = df.select_dtypes(include=[np.number]).columns
        if len(num_cols) > 0:
            stats["numeric_describe"] = df[num_cols].describe().to_dict()
        obj_cols = df.select_dtypes(include=["object"]).columns
        if len(obj_cols) > 0:
            topk: Dict[str, Any] = {}
            for c in obj_cols[:10]:
                vc = df[c].value_counts(dropna=True).head(3)
                topk[c] = {str(k): int(v) for k, v in vc.to_dict().items()}
            stats["top_values"] = topk
        return stats

    # -------- Quality --------
    def quality(self, rr: ReadResult, sheet: Optional[str] = None) -> Dict[str, Any]:
        df = self._select_df(rr, sheet)
        res: Dict[str, Any] = {
            "rows": int(df.shape[0]),
            "columns": int(df.shape[1]),
            "missing": df.isna().sum().to_dict(),
            "duplicate_rows": int(df.duplicated().sum()),
        }
        # simple IQR outlier detection per numeric column
        outliers: Dict[str, int] = {}
        for c in df.select_dtypes(include=[np.number]).columns:
            s = df[c].dropna()
            if s.empty:
                outliers[c] = 0
                continue
            q1, q3 = s.quantile(0.25), s.quantile(0.75)
            iqr = q3 - q1
            lower, upper = q1 - 1.5 * iqr, q3 + 1.5 * iqr
            outliers[c] = int(((s < lower) | (s > upper)).sum())
        res["outliers_iqr"] = outliers
        return res

    # -------- SQL Schema --------
    def sql_schema(self, rr: ReadResult, sheet: Optional[str] = None, table_name: Optional[str] = None, db: str = "sqlite") -> str:
        df = self._select_df(rr, sheet)
        if not table_name:
            table_name = Path(rr.file_path).stem.replace(" ", "_").replace("-", "_")
        lines: List[str] = [f"-- schema for {table_name}", f"CREATE TABLE {table_name} (", "    id INTEGER PRIMARY KEY AUTOINCREMENT,"]
        for c in df.columns:
            name = str(c).strip().replace(" ", "_").replace("-", "_") or "col"
            sql_type = self._infer_sql_type(df[c], db)
            lines.append(f"    {name} {sql_type},")
        if len(lines) > 2:
            lines[-1] = lines[-1].rstrip(',')
        lines.append(");")
        return "\n".join(lines)

    # -------- Helpers --------
    def _select_df(self, rr: ReadResult, sheet: Optional[str]) -> pd.DataFrame:
        if isinstance(rr.data, dict):
            # choose sheet
            chosen = sheet or next(iter(rr.data.keys()))
            return rr.data[chosen]
        return rr.data

    def _infer_sql_type(self, s: pd.Series, db: str) -> str:
        if pd.api.types.is_integer_dtype(s):
            return "INTEGER"
        if pd.api.types.is_float_dtype(s):
            return "REAL"
        if pd.api.types.is_bool_dtype(s):
            return "BOOLEAN"
        if pd.api.types.is_datetime64_any_dtype(s):
            return "TIMESTAMP"
        # fallback text
        return "TEXT"

