from __future__ import annotations

import base64
import io
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd

# Optional heavy deps
try:  # pragma: no cover - optional
    import matplotlib.pyplot as plt
    import seaborn as sns
except Exception:  # pragma: no cover - optional
    plt = None
    sns = None


@dataclass
class HTMLReport:
    html_path: str
    images: List[str]


def _ensure_dir(path: Path) -> None:
    path.mkdir(parents=True, exist_ok=True)


def _fig_to_base64() -> str:
    buf = io.BytesIO()
    assert plt is not None
    plt.savefig(buf, format="png", bbox_inches="tight")
    plt.close()
    buf.seek(0)
    return base64.b64encode(buf.read()).decode("ascii")


def _maybe_plot_histograms(df: pd.DataFrame) -> List[str]:
    if plt is None or sns is None:
        return []
    images: List[str] = []
    num_cols = df.select_dtypes(include=["number"]).columns
    for c in list(num_cols)[:6]:  # limit number of charts
        plt.figure(figsize=(4, 3))
        sns.histplot(df[c].dropna(), kde=True)
        plt.title(f"Histogram: {c}")
        images.append(f"data:image/png;base64,{_fig_to_base64()}")
    return images


def _maybe_plot_corr(df: pd.DataFrame) -> Optional[str]:
    if plt is None or sns is None:
        return None
    num_df = df.select_dtypes(include=["number"]).dropna()
    if num_df.shape[1] < 2:
        return None
    plt.figure(figsize=(5, 4))
    sns.heatmap(num_df.corr(numeric_only=True), annot=False, cmap="Blues")
    plt.title("Correlation Heatmap")
    return f"data:image/png;base64,{_fig_to_base64()}"


def _render_html(title: str, sections: List[str]) -> str:
    head = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>{title}</title>
<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; padding: 16px; }
section { margin-bottom: 24px; }
h2 { border-bottom: 1px solid #eee; padding-bottom: 6px; }
pre { background: #f8f8f8; padding: 12px; overflow: auto; }
img { max-width: 100%; height: auto; }
.grid { display: grid; grid-template-columns: repeat(auto-fill,minmax(260px,1fr)); gap: 12px; }
.card { border: 1px solid #eee; padding: 8px; border-radius: 8px; background: #fff; }
</style>
</head>
<body>
<h1>{title}</h1>
{content}
</body>
</html>
"""
    return head.replace("{title}", title).replace("{content}", "\n".join(sections))


def generate_html_report(
    summary: Dict[str, Any],
    df: pd.DataFrame,
    *,
    output_dir: Optional[str] = None,
    title: str = "Data Analysis Report",
    include_charts: bool = True,
) -> HTMLReport:
    outdir = Path(output_dir or "temp/reports")
    _ensure_dir(outdir)

    sections: List[str] = []

    # Summary block
    sections.append("<section><h2>Summary</h2><pre>" + _safe_json(summary) + "</pre></section>")

    images: List[str] = []
    if include_charts:
        # Histograms grid
        hist_imgs = _maybe_plot_histograms(df)
        if hist_imgs:
            sections.append("<section><h2>Histograms</h2><div class=\"grid\">")
            for uri in hist_imgs:
                sections.append(f"<div class=\"card\"><img src=\"{uri}\" /></div>")
                images.append(uri)
            sections.append("</div></section>")
        # Correlation heatmap
        corr_img = _maybe_plot_corr(df)
        if corr_img:
            sections.append("<section><h2>Correlation</h2>")
            sections.append(f"<img src=\"{corr_img}\" />")
            sections.append("</section>")
            images.append(corr_img)

    html = _render_html(title, sections)
    html_path = outdir / "report.html"
    html_path.write_text(html, encoding="utf-8")

    return HTMLReport(str(html_path), images)


def _safe_json(obj: Dict[str, Any]) -> str:
    # lightweight pretty without importing json to keep deps light here
    import json as _json

    try:
        return _json.dumps(obj, ensure_ascii=False, indent=2)
    except Exception:
        return str(obj)

