from __future__ import annotations
from pathlib import Path
from typing import Optional, List
import pandas as pd


def infer_sql_type(s: pd.Series, db: str = "sqlite") -> str:
    if pd.api.types.is_integer_dtype(s):
        return "INTEGER"
    if pd.api.types.is_float_dtype(s):
        return "REAL"
    if pd.api.types.is_bool_dtype(s):
        return "BOOLEAN"
    if pd.api.types.is_datetime64_any_dtype(s):
        return "TIMESTAMP"
    return "TEXT"


def generate_table_schema(df: pd.DataFrame, file_path: str, table_name: Optional[str] = None, db: str = "sqlite") -> str:
    if not table_name:
        table_name = Path(file_path).stem.replace(" ", "_").replace("-", "_")
    lines: List[str] = [
        f"-- schema for {table_name}",
        f"CREATE TABLE {table_name} (",
        "    id INTEGER PRIMARY KEY AUTOINCREMENT,",
    ]
    for c in df.columns:
        name = str(c).strip().replace(" ", "_").replace("-", "_") or "col"
        lines.append(f"    {name} {infer_sql_type(df[c], db)},")
    if len(lines) > 2:
        lines[-1] = lines[-1].rstrip(',')
    lines.append(");")
    return "\n".join(lines)

