from __future__ import annotations

import json
from pathlib import Path
from typing import Optional, List

try:
    from mcp.server.fastmcp import <PERSON><PERSON><PERSON>
except Exception:
    from mcp.server import FastMCP  # type: ignore

from .core.engine import DataAnalysisEngine
from .generators.html import generate_html_report
from .generators.json import generate_json_report


class DataAnalysisMCPServer:
    def __init__(self, max_rows: int = 10000) -> None:
        self.engine = DataAnalysisEngine(max_rows=max_rows)
        self.mcp = FastMCP("DataAnalysisMCP")
        self._register_tools()

    def _register_tools(self) -> None:
        mcp = self.mcp
        engine = self.engine

        @mcp.tool()
        def analyze_file(file_path: str, analysis_type: str = "basic", sheet: Optional[str] = None) -> str:
            rr = engine.read(file_path)
            if analysis_type == "basic":
                res = engine.structure(rr)
            elif analysis_type in ("statistical", "statistics"):
                res = engine.statistics(rr, sheet=sheet)
            elif analysis_type in ("quality",):
                res = engine.quality(rr, sheet=sheet)
            elif analysis_type == "comprehensive":
                res = {
                    "structure": engine.structure(rr),
                    "statistics": engine.statistics(rr, sheet=sheet),
                    "quality": engine.quality(rr, sheet=sheet),
                }
            else:
                res = {"error": f"unknown analysis_type: {analysis_type}"}
            return json.dumps(res, ensure_ascii=False, indent=2)

        @mcp.tool()
        def get_file_structure(file_path: str) -> str:
            rr = engine.read(file_path)
            res = engine.structure(rr)
            return json.dumps(res, ensure_ascii=False, indent=2)

        @mcp.tool()
        def get_data_statistics(file_path: str, sheet: Optional[str] = None, columns: Optional[List[str]] = None) -> str:
            rr = engine.read(file_path)
            res = engine.statistics(rr, sheet=sheet, columns=columns)
            return json.dumps(res, ensure_ascii=False, indent=2)

        @mcp.tool()
        def check_data_quality(file_path: str, sheet: Optional[str] = None) -> str:
            rr = engine.read(file_path)
            res = engine.quality(rr, sheet=sheet)
            return json.dumps(res, ensure_ascii=False, indent=2)

        @mcp.tool()
        def generate_sql_schema(file_path: str, sheet: Optional[str] = None, table_name: Optional[str] = None, database_type: str = "sqlite") -> str:
            rr = engine.read(file_path)
            sql = engine.sql_schema(rr, sheet=sheet, table_name=table_name, db=database_type)
            return sql

        @mcp.tool()
        def generate_report(
            file_path: str,
            output_format: str = "html",
            analysis_type: str = "comprehensive",
            sheet: Optional[str] = None,
            include_charts: bool = True,
        ) -> str:
            """生成可视化或JSON报告。"""
            rr = engine.read(file_path)
            if analysis_type == "basic":
                summary = engine.structure(rr)
            elif analysis_type in ("statistical", "statistics"):
                summary = engine.statistics(rr, sheet=sheet)
            elif analysis_type in ("quality",):
                summary = engine.quality(rr, sheet=sheet)
            else:
                summary = {
                    "structure": engine.structure(rr),
                    "statistics": engine.statistics(rr, sheet=sheet),
                    "quality": engine.quality(rr, sheet=sheet),
                }

            if output_format.lower() == "html":
                df = engine._select_df(rr, sheet)
                report = generate_html_report(summary, df, include_charts=include_charts)
                return json.dumps({"report_path": report.html_path, "images": report.images}, ensure_ascii=False, indent=2)
            elif output_format.lower() == "json":
                report = generate_json_report(summary)
                return report.json_text
            else:
                return json.dumps({"error": f"unsupported output_format: {output_format}"}, ensure_ascii=False, indent=2)

    def run(self) -> None:
        self.mcp.run()


if __name__ == "__main__":
    DataAnalysisMCPServer().run()