#!/usr/bin/env python3
"""
调试脚本：用于测试数据分析引擎的各个组件
"""

import json
import logging
import sys
from pathlib import Path
from typing import Any, Dict

# 添加源码路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from data_analysis_mcp.core.engine import DataAnalysisEngine

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_engine.log')
    ]
)

logger = logging.getLogger(__name__)


def create_test_data():
    """创建测试数据文件"""
    test_data_dir = Path("tests/data")
    test_data_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建CSV测试文件
    csv_content = """name,age,salary,department,join_date
Alice,25,50000,Engineering,2023-01-15
Bob,30,60000,Marketing,2022-06-20
Charlie,35,70000,Engineering,2021-03-10
<PERSON>,28,55000,Sales,2023-02-28
Eve,32,65000,Marketing,2020-11-05
<PERSON>,29,58000,Engineering,2022-09-12
Grace,26,52000,Sales,2023-04-18
Henry,33,68000,Engineering,2021-07-22
Ivy,27,54000,Marketing,2022-12-03
Jack,31,62000,Sales,2020-08-14"""
    
    csv_file = test_data_dir / "sample.csv"
    csv_file.write_text(csv_content, encoding='utf-8')
    logger.info(f"Created test CSV file: {csv_file}")
    
    return csv_file


def test_engine_basic():
    """测试引擎基本功能"""
    logger.info("=== 测试引擎基本功能 ===")
    
    # 创建测试数据
    test_file = create_test_data()
    
    # 初始化引擎
    engine = DataAnalysisEngine(max_rows=1000)
    logger.info("Engine initialized")
    
    try:
        # 测试文件读取
        logger.info("Testing file reading...")
        read_result = engine.read(str(test_file))
        logger.info(f"File type: {read_result.file_type}")
        logger.info(f"Data shape: {read_result.data.shape if hasattr(read_result.data, 'shape') else 'N/A'}")
        
        # 测试结构分析
        logger.info("Testing structure analysis...")
        structure = engine.structure(read_result)
        logger.info(f"Structure: {json.dumps(structure, indent=2, ensure_ascii=False)}")
        
        # 测试统计分析
        logger.info("Testing statistical analysis...")
        statistics = engine.statistics(read_result)
        logger.info(f"Statistics keys: {list(statistics.keys())}")
        
        # 测试质量检查
        logger.info("Testing quality check...")
        quality = engine.quality(read_result)
        logger.info(f"Quality keys: {list(quality.keys())}")
        
        # 测试SQL生成
        logger.info("Testing SQL schema generation...")
        sql_schema = engine.sql_schema(read_result, table_name="employees", db="sqlite")
        logger.info(f"SQL Schema:\n{sql_schema}")
        
        logger.info("=== 所有测试通过 ===")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)
        return False


def test_error_conditions():
    """测试错误条件"""
    logger.info("=== 测试错误条件 ===")
    
    engine = DataAnalysisEngine(max_rows=1000)
    
    # 测试不存在的文件
    try:
        engine.read("nonexistent_file.csv")
        logger.error("应该抛出FileNotFoundError")
        return False
    except FileNotFoundError:
        logger.info("✓ 正确处理不存在的文件")
    
    # 测试不安全的路径
    try:
        engine.read("../../../etc/passwd")
        logger.error("应该抛出ValueError")
        return False
    except ValueError:
        logger.info("✓ 正确处理不安全的路径")
    
    # 测试不支持的文件类型
    try:
        test_file = Path("test.txt")
        test_file.write_text("test content")
        engine.read(str(test_file))
        test_file.unlink()
        logger.error("应该抛出ValueError")
        return False
    except ValueError:
        logger.info("✓ 正确处理不支持的文件类型")
        test_file.unlink(missing_ok=True)
    
    logger.info("=== 错误条件测试通过 ===")
    return True


def benchmark_performance():
    """性能基准测试"""
    logger.info("=== 性能基准测试 ===")
    
    import time
    import pandas as pd
    
    # 创建大一些的测试数据
    test_data_dir = Path("tests/data")
    test_data_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成更大的数据集
    data = {
        'id': range(1000),
        'value1': [i * 1.5 for i in range(1000)],
        'value2': [i ** 0.5 for i in range(1000)],
        'category': [f'cat_{i % 10}' for i in range(1000)],
        'flag': [i % 2 == 0 for i in range(1000)]
    }
    df = pd.DataFrame(data)
    large_csv = test_data_dir / "large_sample.csv"
    df.to_csv(large_csv, index=False)
    
    engine = DataAnalysisEngine(max_rows=10000)
    
    # 测试读取性能
    start_time = time.time()
    read_result = engine.read(str(large_csv))
    read_time = time.time() - start_time
    logger.info(f"读取时间: {read_time:.3f}秒")
    
    # 测试分析性能
    start_time = time.time()
    structure = engine.structure(read_result)
    structure_time = time.time() - start_time
    logger.info(f"结构分析时间: {structure_time:.3f}秒")
    
    start_time = time.time()
    statistics = engine.statistics(read_result)
    stats_time = time.time() - start_time
    logger.info(f"统计分析时间: {stats_time:.3f}秒")
    
    start_time = time.time()
    quality = engine.quality(read_result)
    quality_time = time.time() - start_time
    logger.info(f"质量检查时间: {quality_time:.3f}秒")
    
    # 清理
    large_csv.unlink()
    
    logger.info("=== 性能测试完成 ===")
    return True


def main():
    """主函数"""
    logger.info("开始调试数据分析引擎")
    
    success = True
    
    # 运行基本功能测试
    if not test_engine_basic():
        success = False
    
    # 运行错误条件测试
    if not test_error_conditions():
        success = False
    
    # 运行性能测试
    if not benchmark_performance():
        success = False
    
    if success:
        logger.info("🎉 所有调试测试通过！")
        return 0
    else:
        logger.error("❌ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
