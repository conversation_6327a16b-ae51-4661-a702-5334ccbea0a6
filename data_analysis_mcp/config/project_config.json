{"project": {"name": "data-analysis-mcp", "version": "0.1.0", "description": "高性能数据分析MCP服务，支持多种数据格式和本地分析能力", "author": "Data Analysis MCP Team", "license": "MIT", "python_version": ">=3.10"}, "development": {"environment": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "PYTHONDONTWRITEBYTECODE": "1", "PYTHONUNBUFFERED": "1", "LOG_LEVEL": "DEBUG", "MAX_ROWS": "10000", "CACHE_ENABLED": "true", "CACHE_TTL": "3600"}, "commands": {"install": "uv sync", "test": "uv run pytest -v --cov=data_analysis_mcp", "test_watch": "uv run pytest-watch", "lint": "uv run ruff check src tests", "format": "uv run black src tests && uv run isort src tests", "type_check": "uv run mypy src", "dev_server": "uv run python -m data_analysis_mcp.server", "build": "uv build", "docs": "uv run mkdocs serve"}, "debugging": {"enabled": true, "log_file": "logs/debug.log", "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "breakpoints": {"server_startup": "data_analysis_mcp.server:DataAnalysisMCPServer.__init__", "file_read": "data_analysis_mcp.core.engine:DataAnalysisEngine.read", "analysis": "data_analysis_mcp.core.engine:DataAnalysisEngine.statistics"}}}, "mcp_server": {"name": "DataAnalysisMCP", "version": "1.0.0", "capabilities": {"tools": true, "resources": false, "prompts": false, "logging": true}, "configuration": {"max_rows": 10000, "timeout": 300, "memory_limit": "1GB", "supported_formats": ["csv", "excel", "json", "parquet", "sqlite"], "analysis_types": ["basic", "statistical", "quality", "comprehensive"], "output_formats": ["json", "html", "pdf"]}, "tools": [{"name": "analyze_file", "description": "分析数据文件，支持多种分析类型", "parameters": {"file_path": {"type": "string", "required": true, "description": "数据文件路径"}, "analysis_type": {"type": "string", "default": "basic", "enum": ["basic", "statistical", "quality", "comprehensive"], "description": "分析类型"}, "sheet": {"type": "string", "required": false, "description": "Excel工作表名称"}}}, {"name": "get_file_structure", "description": "获取文件结构信息", "parameters": {"file_path": {"type": "string", "required": true, "description": "数据文件路径"}}}, {"name": "get_data_statistics", "description": "获取数据统计信息", "parameters": {"file_path": {"type": "string", "required": true, "description": "数据文件路径"}, "sheet": {"type": "string", "required": false, "description": "Excel工作表名称"}, "columns": {"type": "array", "items": {"type": "string"}, "required": false, "description": "指定分析的列"}}}, {"name": "check_data_quality", "description": "检查数据质量", "parameters": {"file_path": {"type": "string", "required": true, "description": "数据文件路径"}, "sheet": {"type": "string", "required": false, "description": "Excel工作表名称"}}}, {"name": "generate_sql_schema", "description": "生成SQL表结构", "parameters": {"file_path": {"type": "string", "required": true, "description": "数据文件路径"}, "sheet": {"type": "string", "required": false, "description": "Excel工作表名称"}, "table_name": {"type": "string", "required": false, "description": "表名"}, "database_type": {"type": "string", "default": "sqlite", "enum": ["sqlite", "postgresql", "mysql", "mssql"], "description": "数据库类型"}}}, {"name": "generate_report", "description": "生成分析报告", "parameters": {"file_path": {"type": "string", "required": true, "description": "数据文件路径"}, "output_format": {"type": "string", "default": "html", "enum": ["html", "json", "pdf"], "description": "输出格式"}, "analysis_type": {"type": "string", "default": "comprehensive", "enum": ["basic", "statistical", "quality", "comprehensive"], "description": "分析类型"}, "sheet": {"type": "string", "required": false, "description": "Excel工作表名称"}, "include_charts": {"type": "boolean", "default": true, "description": "是否包含图表"}}}]}, "client_configurations": {"claude_desktop": {"mcpServers": {"data-analysis": {"command": "uv", "args": ["run", "python", "-m", "data_analysis_mcp.server"], "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src"}}}}, "claude_desktop_dev": {"mcpServers": {"data-analysis-dev": {"command": "uv", "args": ["run", "python", "-m", "data_analysis_mcp.server"], "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src", "LOG_LEVEL": "DEBUG", "MAX_ROWS": "1000"}}}}, "vscode_mcp": {"mcpServers": {"data-analysis": {"command": "${workspaceFolder}/data_analysis_mcp/.venv/bin/python", "args": ["-m", "data_analysis_mcp.server"], "cwd": "${workspaceFolder}/data_analysis_mcp", "env": {"PYTHONPATH": "${workspaceFolder}/data_analysis_mcp/src"}}}}}, "testing": {"pytest": {"testpaths": ["tests"], "python_files": ["test_*.py", "*_test.py"], "python_classes": ["Test*"], "python_functions": ["test_*"], "addopts": ["--cov=data_analysis_mcp", "--cov-report=term-missing", "--cov-report=html", "--cov-report=xml", "--strict-markers", "--strict-config", "-ra"], "markers": ["slow: marks tests as slow", "integration: marks tests as integration tests", "unit: marks tests as unit tests"]}, "coverage": {"source": ["src/data_analysis_mcp"], "omit": ["*/tests/*", "*/test_*", "*/__pycache__/*"], "fail_under": 80}}, "deployment": {"production": {"environment": {"LOG_LEVEL": "INFO", "MAX_ROWS": "50000", "CACHE_ENABLED": "true", "CACHE_TTL": "7200"}, "performance": {"max_memory": "2GB", "timeout": 600, "max_concurrent_requests": 10}}, "docker": {"image": "data-analysis-mcp", "tag": "latest", "ports": ["8000:8000"], "volumes": ["./data:/app/data", "./logs:/app/logs"], "environment": {"PYTHONPATH": "/app/src", "LOG_LEVEL": "INFO"}}}, "monitoring": {"logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "handlers": [{"type": "file", "filename": "logs/app.log", "max_bytes": 10485760, "backup_count": 5}, {"type": "console", "level": "INFO"}]}, "metrics": {"enabled": true, "endpoint": "/metrics", "include": ["request_count", "request_duration", "memory_usage", "file_processing_time"]}}}