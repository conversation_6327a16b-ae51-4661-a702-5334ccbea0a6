{"project_status": {"name": "data-analysis-mcp", "version": "0.1.0", "status": "ready_for_deployment", "last_tested": "2025-08-23", "test_results": {"unit_tests": "PASSED", "integration_tests": "PASSED", "mcp_server_tests": "PASSED", "engine_tests": "PASSED", "error_handling_tests": "PASSED"}}, "configuration_files": {"main_config": "config/project_config.json", "debug_config": "config/debug_config.json", "pip_config": "pip.conf", "uv_config": "uv.toml", "vscode_debug": ".vscode/launch.json", "docker_compose": "docker-compose.yml", "dockerfile_prod": "Dockerfile", "dockerfile_dev": "Dockerfile.dev"}, "deployment_options": {"local_development": {"command": "uv run python -m data_analysis_mcp.server", "environment": {"PYTHONPATH": "src", "LOG_LEVEL": "DEBUG", "MAX_ROWS": "1000"}, "requirements": ["uv", "python>=3.10"]}, "claude_desktop": {"config_file": "examples/mcp_config.json", "setup_steps": ["1. 确保项目已安装: uv sync", "2. 将配置添加到Claude Desktop配置文件", "3. 重启<PERSON>", "4. 测试工具可用性"]}, "docker_development": {"command": "docker-compose up data-analysis-mcp-dev", "features": ["热重载", "调试端口5678", "完整日志"], "ports": ["8001:8000", "5678:5678"]}, "docker_production": {"command": "docker-compose up -d data-analysis-mcp", "features": ["优化性能", "健康检查", "自动重启"], "ports": ["8000:8000"]}}, "available_tools": [{"name": "analyze_file", "description": "分析数据文件，支持多种分析类型", "parameters": ["file_path", "analysis_type", "sheet"], "analysis_types": ["basic", "statistical", "quality", "comprehensive"]}, {"name": "get_file_structure", "description": "获取文件结构信息", "parameters": ["file_path"]}, {"name": "get_data_statistics", "description": "获取数据统计信息", "parameters": ["file_path", "sheet", "columns"]}, {"name": "check_data_quality", "description": "检查数据质量", "parameters": ["file_path", "sheet"]}, {"name": "generate_sql_schema", "description": "生成SQL表结构", "parameters": ["file_path", "sheet", "table_name", "database_type"], "database_types": ["sqlite", "postgresql", "mysql", "mssql"]}, {"name": "generate_report", "description": "生成分析报告", "parameters": ["file_path", "output_format", "analysis_type", "sheet", "include_charts"], "output_formats": ["html", "json", "pdf"]}], "supported_formats": [{"format": "CSV", "extensions": [".csv"], "features": ["完整支持", "自动类型推断", "大文件处理"]}, {"format": "Excel", "extensions": [".xlsx", ".xls"], "features": ["多工作表支持", "格式保留", "公式识别"]}, {"format": "JSON", "extensions": [".json"], "features": ["嵌套结构", "数组处理", "类型保留"]}, {"format": "Pa<PERSON><PERSON>", "extensions": [".parquet"], "features": ["高性能", "压缩存储", "列式存储"]}, {"format": "SQLite", "extensions": [".db", ".sqlite"], "features": ["关系数据", "SQL查询", "事务支持"]}], "performance_optimizations": {"chinese_mirrors": {"pip_index": "https://pypi.tuna.tsinghua.edu.cn/simple", "backup_mirrors": ["https://mirrors.aliyun.com/pypi/simple/", "https://pypi.douban.com/simple/"]}, "memory_management": {"max_rows_default": 10000, "max_rows_production": 50000, "streaming_support": true, "cache_enabled": true}, "processing_limits": {"timeout_seconds": 300, "max_file_size": "100MB", "concurrent_requests": 10}}, "security_features": {"path_validation": {"blocked_patterns": ["..", "~", "/etc", "/var", "/usr"], "allowed_extensions": [".csv", ".xlsx", ".xls", ".json", ".parquet"]}, "resource_limits": {"max_memory": "2GB", "max_processing_time": 300, "max_concurrent_files": 5}, "error_handling": {"detailed_logging": true, "sanitized_errors": true, "graceful_degradation": true}}, "debugging_tools": {"debug_script": "debug_engine.py", "mcp_test_script": "test_mcp_server.py", "log_files": ["logs/debug.log", "logs/app.log"], "vscode_configs": ["Debug MCP Server", "Debug Tests", "Debug Engine Only", "Profile MCP Server"]}, "monitoring": {"health_checks": true, "metrics_collection": true, "performance_tracking": true, "error_reporting": true}, "next_steps": ["1. 根据需要选择部署方式", "2. 配置环境变量和资源限制", "3. 运行测试验证功能", "4. 监控性能和错误", "5. 根据使用情况优化配置"]}