{"debug": {"enabled": true, "level": "DEBUG", "output": {"console": true, "file": "logs/debug.log", "structured": true}, "components": {"server": {"enabled": true, "log_requests": true, "log_responses": true, "log_errors": true}, "engine": {"enabled": true, "log_file_operations": true, "log_analysis_steps": true, "log_performance": true}, "parsers": {"enabled": true, "log_parsing_details": true, "log_data_samples": false}, "analyzers": {"enabled": true, "log_computation_steps": true, "log_intermediate_results": false}}, "performance": {"profile_enabled": true, "memory_tracking": true, "timing_enabled": true, "slow_query_threshold": 5.0}}, "testing": {"test_data_path": "tests/data", "sample_files": {"csv": "tests/data/sample.csv", "excel": "tests/data/sample.xlsx", "json": "tests/data/sample.json", "parquet": "tests/data/sample.parquet"}, "mock_settings": {"enable_mocks": true, "mock_large_files": true, "mock_network_calls": true}, "fixtures": {"small_dataset": {"rows": 100, "columns": 5, "types": ["int", "float", "string", "datetime", "boolean"]}, "medium_dataset": {"rows": 10000, "columns": 20, "types": ["mixed"]}, "large_dataset": {"rows": 100000, "columns": 50, "types": ["mixed"]}}}, "development_tools": {"hot_reload": {"enabled": true, "watch_paths": ["src/data_analysis_mcp"], "ignore_patterns": ["*.pyc", "__pycache__", "*.log"]}, "code_quality": {"auto_format": true, "auto_lint": true, "pre_commit_hooks": true}, "documentation": {"auto_generate": true, "include_examples": true, "api_docs": true}}, "error_handling": {"detailed_tracebacks": true, "capture_locals": true, "error_reporting": {"enabled": true, "include_context": true, "sanitize_data": true}, "recovery": {"auto_retry": true, "max_retries": 3, "backoff_strategy": "exponential"}}, "security": {"path_validation": {"enabled": true, "allowed_paths": ["/tmp", "./data", "./tests/data"], "blocked_patterns": ["..", "~", "/etc", "/var", "/usr"]}, "file_validation": {"max_file_size": "100MB", "allowed_extensions": [".csv", ".xlsx", ".xls", ".json", ".parquet"], "scan_content": true}, "resource_limits": {"max_memory": "1GB", "max_processing_time": 300, "max_concurrent_files": 5}}, "mcp_debug": {"protocol_logging": true, "message_validation": true, "capability_testing": true, "client_simulation": {"enabled": true, "test_scenarios": ["basic_file_analysis", "large_file_handling", "error_conditions", "concurrent_requests"]}}, "profiling": {"enabled": false, "profiler": "cProfile", "output_format": "stats", "sort_by": "cumulative", "top_functions": 20, "memory_profiling": {"enabled": false, "interval": 0.1, "precision": 3}}, "benchmarking": {"enabled": false, "test_files": {"small": "tests/data/benchmark_small.csv", "medium": "tests/data/benchmark_medium.csv", "large": "tests/data/benchmark_large.csv"}, "metrics": ["processing_time", "memory_usage", "cpu_usage", "io_operations"], "iterations": 10, "warmup_runs": 3}}