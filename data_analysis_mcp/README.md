# 数据分析MCP服务

一个基于uvx环境的高性能数据分析MCP服务，支持多种数据格式处理、本地分析能力和深度MCP集成。

## 特性

### 🚀 核心功能
- **多格式支持**: Excel (.xlsx, .xls), CSV, JSON, Parquet, SQLite
- **本地分析**: 无需付费API，完全本地运行
- **统计分析**: 描述性统计、分布分析、相关性分析
- **数据质量**: 缺失值检测、异常值识别、重复数据检查
- **可视化**: 图表生成、HTML报告导出
- **SQL生成**: 自动生成表结构和数据导入脚本

### 🔧 技术特性
- **uvx环境**: 基于现代Python包管理
- **MCP深度集成**: 完整的MCP协议支持
- **插件系统**: 可扩展的数据处理器和分析算法
- **流式处理**: 支持大文件分块处理
- **缓存机制**: 智能结果缓存提升性能

### 🛡️ 安全特性
- 路径验证防止目录遍历
- 文件类型检查
- 内存使用限制
- 处理时间限制

## 快速开始

### 安装

```bash
# 使用uvx安装
uvx install data-analysis-mcp

# 或从源码安装
git clone <repo>
cd data_analysis_mcp
uv sync
```

### 启动服务

```bash
# 直接启动
uvx run data-analysis-mcp

# 或使用uv
uv run python -m data_analysis_mcp.server
```

### MCP配置

在Claude Desktop配置中添加：

```json
{
  "mcpServers": {
    "data-analysis": {
      "command": "uvx",
      "args": ["run", "data-analysis-mcp"],
      "env": {}
    }
  }
}
```

## 使用示例

### 基本数据分析

```python
# 分析Excel文件
result = await mcp.call_tool("analyze_file", {
    "file_path": "/path/to/data.xlsx",
    "analysis_type": "comprehensive"
})

# 生成可视化报告
report = await mcp.call_tool("generate_report", {
    "file_path": "/path/to/data.csv",
    "output_format": "html",
    "include_charts": true
})
```

### 数据质量检查

```python
# 检查数据质量
quality = await mcp.call_tool("check_data_quality", {
    "file_path": "/path/to/data.xlsx",
    "checks": ["missing_values", "duplicates", "outliers"]
})
```

### SQL生成

```python
# 生成表结构
schema = await mcp.call_tool("generate_sql_schema", {
    "file_path": "/path/to/data.csv",
    "table_name": "sales_data",
    "database_type": "postgresql"
})
```

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    MCP客户端 (Claude Desktop)                │
└─────────────────────┬───────────────────────────────────────┘
                      │ MCP协议
┌─────────────────────▼───────────────────────────────────────┐
│                  MCP服务器层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │  工具管理   │ │  资源管理   │ │      提示管理           │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  核心分析引擎                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ 数据解析器  │ │ 分析处理器  │ │      结果生成器         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  插件扩展层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ 格式插件    │ │ 分析插件    │ │      可视化插件         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 工具接口

### analyze_file
分析数据文件，支持多种分析类型和输出格式

### check_data_quality  
执行数据质量检查，识别问题和异常

### generate_report
生成可视化分析报告，支持HTML和PDF格式

### generate_sql_schema
为数据文件生成SQL表结构和导入脚本

### compare_datasets
比较多个数据集的差异和相似性

### transform_data
执行数据转换和清洗操作

## 开发

### 项目结构

```
data_analysis_mcp/
├── pyproject.toml          # 项目配置
├── uv.lock                 # 依赖锁定
├── src/
│   └── data_analysis_mcp/
│       ├── __init__.py
│       ├── server.py       # MCP服务器
│       ├── core/           # 核心引擎
│       ├── parsers/        # 数据解析器
│       ├── analyzers/      # 分析器
│       ├── generators/     # 结果生成器
│       ├── plugins/        # 插件系统
│       └── utils/          # 工具函数
├── tests/                  # 测试
├── docs/                   # 文档
└── examples/               # 示例
```

### 运行测试

```bash
uv run pytest
```

### 构建文档

```bash
uv run mkdocs serve
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
