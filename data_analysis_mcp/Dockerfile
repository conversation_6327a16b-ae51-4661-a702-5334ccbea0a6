# 多阶段构建的生产环境Dockerfile
FROM python:3.11-slim as builder

# 设置构建参数
ARG PYTHON_VERSION=3.11

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY pyproject.toml uv.lock ./
COPY src/ ./src/

# 安装Python依赖
RUN uv sync --frozen --no-dev

# 构建wheel包
RUN uv build

# 生产环境镜像
FROM python:3.11-slim as production

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制wheel包
COPY --from=builder /app/dist/*.whl /tmp/

# 安装应用
RUN pip install /tmp/*.whl && rm /tmp/*.whl

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/config && \
    chown -R appuser:appuser /app

# 复制配置文件
COPY config/ ./config/

# 切换到非root用户
USER appuser

# 设置环境变量
ENV PYTHONPATH=/app/src \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    LOG_LEVEL=INFO

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "data_analysis_mcp.server"]
