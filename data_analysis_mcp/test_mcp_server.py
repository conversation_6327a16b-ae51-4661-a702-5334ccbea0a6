#!/usr/bin/env python3
"""
MCP服务器功能测试脚本
"""

import asyncio
import json
import logging
import sys
import tempfile
from pathlib import Path

# 添加源码路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from data_analysis_mcp.server import DataAnalysisMCPServer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_test_data():
    """创建测试数据文件"""
    # 创建临时CSV文件
    csv_content = """name,age,salary,department,join_date
Alice,25,50000,Engineering,2023-01-15
Bob,30,60000,Marketing,2022-06-20
Charlie,35,70000,Engineering,2021-03-10
Diana,28,55000,Sales,2023-02-28
Eve,32,65000,Marketing,2020-11-05"""
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    temp_file.write(csv_content)
    temp_file.close()
    
    logger.info(f"Created test CSV file: {temp_file.name}")
    return temp_file.name


async def test_mcp_server_tools():
    """测试MCP服务器的工具功能"""
    logger.info("=== 测试MCP服务器工具功能 ===")

    # 创建测试数据
    test_file = create_test_data()

    try:
        # 初始化MCP服务器
        server = DataAnalysisMCPServer(max_rows=1000)
        logger.info("MCP服务器初始化成功")

        # 获取工具列表
        tools = await server.mcp.list_tools()
        logger.info(f"可用工具: {[tool.name for tool in tools]}")

        # 测试analyze_file工具
        logger.info("测试 analyze_file 工具...")
        result = await server.mcp.call_tool("analyze_file", {
            "file_path": test_file,
            "analysis_type": "basic"
        })
        logger.info("analyze_file 结果:")
        logger.info(result[:200] + "..." if len(result) > 200 else result)
        
        # 测试get_file_structure工具
        logger.info("测试 get_file_structure 工具...")
        result = await server.mcp.call_tool("get_file_structure", {
            "file_path": test_file
        })
        logger.info("get_file_structure 结果:")
        logger.info(result[:200] + "..." if len(result) > 200 else result)

        # 测试get_data_statistics工具
        logger.info("测试 get_data_statistics 工具...")
        result = await server.mcp.call_tool("get_data_statistics", {
            "file_path": test_file
        })
        logger.info("get_data_statistics 结果:")
        logger.info(result[:200] + "..." if len(result) > 200 else result)

        # 测试check_data_quality工具
        logger.info("测试 check_data_quality 工具...")
        result = await server.mcp.call_tool("check_data_quality", {
            "file_path": test_file
        })
        logger.info("check_data_quality 结果:")
        logger.info(result[:200] + "..." if len(result) > 200 else result)

        # 测试generate_sql_schema工具
        logger.info("测试 generate_sql_schema 工具...")
        result = await server.mcp.call_tool("generate_sql_schema", {
            "file_path": test_file,
            "table_name": "employees",
            "database_type": "sqlite"
        })
        logger.info("generate_sql_schema 结果:")
        logger.info(result)

        # 测试generate_report工具
        logger.info("测试 generate_report 工具...")
        result = await server.mcp.call_tool("generate_report", {
            "file_path": test_file,
            "output_format": "json",
            "analysis_type": "comprehensive"
        })
        logger.info("generate_report 结果:")
        logger.info(result[:200] + "..." if len(result) > 200 else result)
        
        logger.info("=== 所有MCP工具测试通过 ===")
        return True
        
    except Exception as e:
        logger.error(f"MCP服务器测试失败: {e}", exc_info=True)
        return False
    
    finally:
        # 清理临时文件
        try:
            Path(test_file).unlink()
            logger.info(f"清理临时文件: {test_file}")
        except Exception:
            pass


async def test_error_handling():
    """测试错误处理"""
    logger.info("=== 测试错误处理 ===")

    server = DataAnalysisMCPServer(max_rows=1000)

    try:
        # 测试不存在的文件
        logger.info("测试不存在的文件...")
        try:
            result = await server.mcp.call_tool("analyze_file", {
                "file_path": "nonexistent_file.csv",
                "analysis_type": "basic"
            })
            logger.error("✗ 未正确处理不存在的文件")
            return False
        except Exception as e:
            if "File not found" in str(e):
                logger.info("✓ 正确处理不存在的文件")
            else:
                logger.error(f"✗ 意外错误: {e}")
                return False

        # 测试不安全的路径
        logger.info("测试不安全的路径...")
        try:
            result = await server.mcp.call_tool("analyze_file", {
                "file_path": "../../../etc/passwd",
                "analysis_type": "basic"
            })
            logger.error("✗ 未正确处理不安全的路径")
            return False
        except Exception as e:
            if "Invalid path" in str(e):
                logger.info("✓ 正确处理不安全的路径")
            else:
                logger.error(f"✗ 意外错误: {e}")
                return False

        # 测试不支持的分析类型
        test_file = create_test_data()
        try:
            logger.info("测试不支持的分析类型...")
            result = await server.mcp.call_tool("analyze_file", {
                "file_path": test_file,
                "analysis_type": "unsupported_type"
            })
            # 这个应该返回包含error的结果，而不是抛出异常
            result_data = json.loads(result[1]['result'])  # 从MCP响应中提取结果
            if "error" in result_data:
                logger.info("✓ 正确处理不支持的分析类型")
            else:
                logger.error("✗ 未正确处理不支持的分析类型")
                return False
        finally:
            Path(test_file).unlink()
        
        logger.info("=== 错误处理测试通过 ===")
        return True
        
    except Exception as e:
        logger.error(f"错误处理测试失败: {e}", exc_info=True)
        return False


async def main():
    """主函数"""
    logger.info("开始MCP服务器功能测试")

    success = True

    # 运行工具功能测试
    if not await test_mcp_server_tools():
        success = False

    # 运行错误处理测试
    if not await test_error_handling():
        success = False

    if success:
        logger.info("🎉 所有MCP服务器测试通过！")
        return 0
    else:
        logger.error("❌ 部分MCP服务器测试失败")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
