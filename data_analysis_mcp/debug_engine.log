2025-08-23 13:46:15,812 - __main__ - INFO - 开始调试数据分析引擎
2025-08-23 13:46:15,812 - __main__ - INFO - === 测试引擎基本功能 ===
2025-08-23 13:46:15,813 - __main__ - INFO - Created test CSV file: tests/data/sample.csv
2025-08-23 13:46:15,813 - __main__ - INFO - Engine initialized
2025-08-23 13:46:15,813 - __main__ - INFO - Testing file reading...
2025-08-23 13:46:15,816 - __main__ - INFO - File type: csv
2025-08-23 13:46:15,816 - __main__ - INFO - Data shape: (10, 5)
2025-08-23 13:46:15,817 - __main__ - INFO - Testing structure analysis...
2025-08-23 13:46:15,817 - __main__ - INFO - Structure: {
  "file_path": "tests/data/sample.csv",
  "file_type": "csv",
  "rows": 10,
  "columns": 5,
  "column_names": [
    "name",
    "age",
    "salary",
    "department",
    "join_date"
  ]
}
2025-08-23 13:46:15,817 - __main__ - INFO - Testing statistical analysis...
2025-08-23 13:46:15,830 - __main__ - INFO - Statistics keys: ['shape', 'columns', 'dtypes', 'missing', 'numeric_describe', 'top_values']
2025-08-23 13:46:15,830 - __main__ - INFO - Testing quality check...
2025-08-23 13:46:15,836 - __main__ - INFO - Quality keys: ['rows', 'columns', 'missing', 'duplicate_rows', 'outliers_iqr']
2025-08-23 13:46:15,836 - __main__ - INFO - Testing SQL schema generation...
2025-08-23 13:46:15,837 - __main__ - INFO - SQL Schema:
-- schema for employees
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    age INTEGER,
    salary INTEGER,
    department TEXT,
    join_date TEXT
);
2025-08-23 13:46:15,837 - __main__ - INFO - === 所有测试通过 ===
2025-08-23 13:46:15,837 - __main__ - INFO - === 测试错误条件 ===
2025-08-23 13:46:15,837 - __main__ - INFO - ✓ 正确处理不存在的文件
2025-08-23 13:46:55,601 - __main__ - INFO - 开始调试数据分析引擎
2025-08-23 13:46:55,601 - __main__ - INFO - === 测试引擎基本功能 ===
2025-08-23 13:46:55,736 - __main__ - INFO - Created test CSV file: tests/data/sample.csv
2025-08-23 13:46:55,736 - __main__ - INFO - Engine initialized
2025-08-23 13:46:55,736 - __main__ - INFO - Testing file reading...
2025-08-23 13:46:55,739 - __main__ - INFO - File type: csv
2025-08-23 13:46:55,740 - __main__ - INFO - Data shape: (10, 5)
2025-08-23 13:46:55,740 - __main__ - INFO - Testing structure analysis...
2025-08-23 13:46:55,740 - __main__ - INFO - Structure: {
  "file_path": "tests/data/sample.csv",
  "file_type": "csv",
  "rows": 10,
  "columns": 5,
  "column_names": [
    "name",
    "age",
    "salary",
    "department",
    "join_date"
  ]
}
2025-08-23 13:46:55,740 - __main__ - INFO - Testing statistical analysis...
2025-08-23 13:46:55,750 - __main__ - INFO - Statistics keys: ['shape', 'columns', 'dtypes', 'missing', 'numeric_describe', 'top_values']
2025-08-23 13:46:55,750 - __main__ - INFO - Testing quality check...
2025-08-23 13:46:55,756 - __main__ - INFO - Quality keys: ['rows', 'columns', 'missing', 'duplicate_rows', 'outliers_iqr']
2025-08-23 13:46:55,756 - __main__ - INFO - Testing SQL schema generation...
2025-08-23 13:46:55,756 - __main__ - INFO - SQL Schema:
-- schema for employees
CREATE TABLE employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    age INTEGER,
    salary INTEGER,
    department TEXT,
    join_date TEXT
);
2025-08-23 13:46:55,756 - __main__ - INFO - === 所有测试通过 ===
2025-08-23 13:46:55,757 - __main__ - INFO - === 测试错误条件 ===
2025-08-23 13:46:55,757 - __main__ - INFO - ✓ 正确处理不存在的文件
2025-08-23 13:46:55,757 - __main__ - INFO - ✓ 正确处理不安全的路径
2025-08-23 13:46:55,757 - __main__ - INFO - ✓ 正确处理不支持的文件类型
2025-08-23 13:46:55,757 - __main__ - INFO - === 错误条件测试通过 ===
2025-08-23 13:46:55,757 - __main__ - INFO - === 性能基准测试 ===
2025-08-23 13:46:55,772 - __main__ - INFO - 读取时间: 0.003秒
2025-08-23 13:46:55,772 - __main__ - INFO - 结构分析时间: 0.000秒
2025-08-23 13:46:55,785 - __main__ - INFO - 统计分析时间: 0.013秒
2025-08-23 13:46:55,794 - __main__ - INFO - 质量检查时间: 0.009秒
2025-08-23 13:46:55,795 - __main__ - INFO - === 性能测试完成 ===
2025-08-23 13:46:55,795 - __main__ - INFO - 🎉 所有调试测试通过！
