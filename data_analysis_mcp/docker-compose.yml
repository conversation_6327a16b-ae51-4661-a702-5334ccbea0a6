version: '3.8'

services:
  data-analysis-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - PYTHON_VERSION=3.11
    container_name: data-analysis-mcp
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app/src
      - LOG_LEVEL=INFO
      - MAX_ROWS=50000
      - CACHE_ENABLED=true
      - CACHE_TTL=7200
    volumes:
      - ./data:/app/data:ro
      - ./logs:/app/logs
      - ./config:/app/config:ro
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mcp-network

  data-analysis-mcp-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: data-analysis-mcp-dev
    restart: "no"
    environment:
      - PYTHONPATH=/app/src
      - LOG_LEVEL=DEBUG
      - MAX_ROWS=10000
      - CACHE_ENABLED=false
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    volumes:
      - .:/app
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "8001:8000"
      - "5678:5678"  # debugpy port
    command: ["python", "-m", "debugpy", "--listen", "0.0.0.0:5678", "--wait-for-client", "-m", "data_analysis_mcp.server"]
    networks:
      - mcp-network

  redis:
    image: redis:7-alpine
    container_name: data-analysis-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - mcp-network

  prometheus:
    image: prom/prometheus:latest
    container_name: data-analysis-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mcp-network

  grafana:
    image: grafana/grafana:latest
    container_name: data-analysis-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
